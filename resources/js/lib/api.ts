import { router } from '@inertiajs/svelte';
import type {
    Prompt,
    Response,
    CreatePromptRequest,
    PromptFilters,
    PaginatedResponse,
    ApiResponse,
    QueueJob
} from '@/types';

class ApiService {
    private baseUrl = '/api';

    private async request<T>(
        endpoint: string, 
        options: RequestInit = {}
    ): Promise<ApiResponse<T>> {
        const url = `${this.baseUrl}${endpoint}`;
        
        const defaultHeaders: HeadersInit = {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        };

        // Get CSRF token from meta tag
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        if (csrfToken) {
            defaultHeaders['X-CSRF-TOKEN'] = csrfToken;
        }

        const config: RequestInit = {
            ...options,
            headers: {
                ...defaultHeaders,
                ...options.headers,
            },
        };

        // Add Content-Type for JSON requests
        if (options.body && typeof options.body === 'string') {
            config.headers = {
                ...config.headers,
                'Content-Type': 'application/json',
            };
        }

        const response = await fetch(url, config);
        
        if (!response.ok) {
            if (response.status === 401) {
                router.visit('/login');
                throw new Error('Unauthorized');
            }
            
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `HTTP ${response.status}`);
        }

        return await response.json();
    }

    // Prompt endpoints
    async getPrompts(filters: PromptFilters = {}, page = 1): Promise<PaginatedResponse<Prompt>> {
        const params = new URLSearchParams();

        Object.entries(filters).forEach(([key, value]) => {
            if (value !== undefined && value !== '') {
                params.append(key, String(value));
            }
        });

        if (page > 1) {
            params.append('page', String(page));
        }

        const queryString = params.toString();
        const endpoint = `/prompts${queryString ? `?${queryString}` : ''}`;

        const response = await this.request<PaginatedResponse<Prompt>>(endpoint);
        // Laravel paginate() returns the pagination data directly, not wrapped in a data property
        return response as unknown as PaginatedResponse<Prompt>;
    }

    async getPrompt(uuid: string): Promise<Prompt> {
        const response = await this.request<Prompt>(`/prompts/${uuid}`);
        return response.data!;
    }

    async createPrompt(data: CreatePromptRequest): Promise<Prompt> {
        const formData = new FormData();

        formData.append('content', data.content);

        if (data.models && data.models.length > 0) {
            data.models.forEach((model, index) => {
                formData.append(`models[${index}]`, model);
            });
        }

        if (data.attachments && data.attachments.length > 0) {
            data.attachments.forEach((file, index) => {
                formData.append(`attachments[${index}]`, file);
            });
        }

        if (data.metadata) {
            formData.append('metadata', JSON.stringify(data.metadata));
        }

        const response = await this.request<Prompt>('/prompts', {
            method: 'POST',
            body: formData,
        });

        return response.data!;
    }

    async updatePrompt(uuid: string, data: Partial<CreatePromptRequest>): Promise<Prompt> {
        const formData = new FormData();

        if (data.content !== undefined) {
            formData.append('content', data.content);
        }

        if (data.models && data.models.length > 0) {
            data.models.forEach((model, index) => {
                formData.append(`models[${index}]`, model);
            });
        }

        if (data.metadata) {
            formData.append('metadata', JSON.stringify(data.metadata));
        }

        // Add method override for PUT request
        formData.append('_method', 'PUT');

        const response = await this.request<Prompt>(`/prompts/${uuid}`, {
            method: 'POST',
            body: formData,
        });

        return response.data!;
    }

    async getPromptStatus(uuid: string): Promise<{ uuid: string; status: string; responses_count: number; processed_at: string | null }> {
        const response = await this.request(`/prompts/${uuid}/status`);
        return response.data!;
    }

    async getPromptResponses(uuid: string): Promise<Response[]> {
        const response = await this.request<Response[]>(`/prompts/${uuid}/responses`);
        return response.data!;
    }

    async deletePrompt(uuid: string): Promise<void> {
        await this.request(`/prompts/${uuid}`, {
            method: 'DELETE',
        });
    }

    async deleteResponse(promptUuid: string, responseUuid: string): Promise<void> {
        await this.request(`/prompts/${promptUuid}/responses/${responseUuid}`, {
            method: 'DELETE',
        });
    }

    // System endpoints
    async getSystemStatus(): Promise<any> {
        const response = await this.request('/system/status');
        return response.data!;
    }

    async getQueueJobs(): Promise<QueueJob[]> {
        const response = await this.request<QueueJob[]>('/system/queue/jobs');
        return response.data!;
    }

    async deleteQueueJob(jobId: number): Promise<void> {
        await this.request(`/system/queue/jobs/${jobId}`, {
            method: 'DELETE',
        });
    }

    async createJob(promptUuid: string, model: string): Promise<void> {
        await this.request(`/prompts/${promptUuid}/jobs`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ model }),
        });
    }

    async getSystemModels(): Promise<string[]> {
        const response = await this.request<Record<string, string>>('/system/models');
        const modelsObject = response.data!;

        // Se for um objeto, extrair as chaves (model IDs)
        if (typeof modelsObject === 'object' && modelsObject !== null) {
            return Object.keys(modelsObject);
        }

        // Se for um array, retornar como está
        if (Array.isArray(modelsObject)) {
            return modelsObject;
        }

        // Fallback para array vazio
        return [];
    }

    async getAvailableModels(): Promise<string[]> {
        const response = await this.request<Record<string, string>>('/system/models');
        const modelsObject = response.data!;

        // Convert object to array of keys (model IDs)
        return Object.keys(modelsObject);
    }

    async getAvailableModelsWithNames(): Promise<Record<string, string>> {
        const response = await this.request<Record<string, string>>('/system/models');
        return response.data!;
    }

    async getSystemStats(): Promise<any> {
        const response = await this.request('/system/stats');
        return response.data!;
    }
}

export const api = new ApiService();
